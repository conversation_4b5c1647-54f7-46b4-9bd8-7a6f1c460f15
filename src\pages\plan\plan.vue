<template>
  <view class="plan-page">
    <NavBar title="计划" :show-back="false" />

    <scroll-view class="content" scroll-y="true">
      <view class="content-wrapper">
        <!-- 日期导航 -->
        <view class="date-nav">
          <view v-for="date in dateList" :key="date.date" class="date-item" :class="{
            'active': date.date === currentDate,
            'today': date.isToday
          }" @click="selectDate(date.date)">
            <text class="date-day" :class="{ 'today-day': date.isToday }">{{ date.day }}</text>
            <text class="date-text" :class="{ 'today-text': date.isToday }">{{ date.text }}</text>
          </view>
        </view>

        <!-- 进度显示 -->
        <view class="progress-section">
          <view class="progress-header">
            <text class="progress-title">今日进度</text>
            <text class="progress-text">{{ completedCount }}/{{ totalCount }}</text>
          </view>
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
          </view>
        </view>

        <!-- 计划列表 -->
        <view v-if="currentPlans.length > 0" class="plan-list">
          <view class="section-header">
            <text class="section-title">{{ currentDateText }}计划</text>
            <text class="manage-plan-button" @click="onManagePlan">计划管理</text>
          </view>
          <PlanCard v-for="plan in currentPlans" :key="plan.id" :id="plan.id" :course-name="plan.courseName"
            :lesson-name="plan.lessonName" :cover="plan.cover" :duration="plan.duration" :progress="plan.progress"
            :learners-count="plan.learnersCount" :is-completed="plan.isCompleted" @click="onPlanClick" @delete="onDeletePlan" />
        </view>

        <!-- 空状态 -->
        <view v-else class="empty-state">
          <text class="empty-title">暂无计划</text>
          <text class="empty-desc">为自己制定一个冥想计划吧</text>
          <view class="custom-plan-button" @click="onCustomPlan">
            <text class="button-text">定制计划</text>
          </view>
        </view>

        <!-- 推荐课程 -->
        <view class="recommend-section">
          <view class="section-header">
            <text class="section-title">推荐课程</text>
            <text class="view-all" @click="onViewAllRecommend">查看全部</text>
          </view>
          <scroll-view class="recommend-scroll" scroll-x="true" show-scrollbar="false">
            <view class="recommend-container">
              <RecommendCard v-for="course in recommendCourses" :key="course.id"
                :id="course.id.toString()"
                :title="course.title"
                :description="course.description || ''"
                :cover="course.cover_url || 'https://picsum.photos/280/160?random=default'"
                :duration="Math.round(course.duration / 60)"
                @click="onCourseClick" @add-to-plan="onAddToPlan" />
            </view>
          </scroll-view>
        </view>
      </view>
    </scroll-view>

    <TabBar />
  </view>
</template>

<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";

import { ref, computed, onMounted, watch } from 'vue'
import NavBar from '@/components/NavBar.vue'
import TabBar from '@/components/TabBar.vue'
import PlanCard from '@/components/PlanCard.vue'
import RecommendCard from '@/components/RecommendCard.vue'
import { PlanApi, type DailyPlan, type PlanItem as ApiPlanItem, type AddToPlanParams } from '@/api/plan'
import { MeditationApi, type MeditationContent } from '@/api/meditation'

// 获取当前真实日期
const getTodayDateString = () => {
  const today = new Date()
  return today.toISOString().split('T')[0]
}

// 获取星期几的中文
const getWeekdayText = (date: Date) => {
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  return weekdays[date.getDay()]
}

// 日期相关
const todayDate = getTodayDateString()
const currentDate = ref(todayDate)

const dateList = computed(() => {
  const today = new Date()
  const dates = []

  for (let i = -3; i <= 3; i++) {
    const date = new Date(today)
    date.setDate(today.getDate() + i)

    const dateStr = date.toISOString().split('T')[0]
    const day = date.getDate()
    const weekday = getWeekdayText(date)

    dates.push({
      date: dateStr,
      day: day,
      text: weekday,
      isToday: dateStr === todayDate
    })
  }

  return dates
})

const currentDateText = computed(() => {
  const item = dateList.value.find(d => d.date === currentDate.value)
  if (item?.isToday) return '今日'

  const selectedDate = new Date(currentDate.value)
  const today = new Date()
  const diffTime = selectedDate.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === -1) return '昨日'
  if (diffDays === 1) return '明日'

  return `${selectedDate.getMonth() + 1}月${selectedDate.getDate()}日`
})

// 定义计划项类型（用于UI显示）
interface PlanItem {
  id: string
  courseName: string
  lessonName: string
  cover: string
  duration: number
  progress: number
  learnersCount: number
  isCompleted: boolean
  meditation_id?: string // 添加meditation_id用于API调用
}

// 转换API数据到UI数据的函数
const convertApiPlanToUiPlan = (apiPlan: ApiPlanItem): PlanItem => {
  const meditation = apiPlan.meditation
  return {
    id: apiPlan.id,
    meditation_id: apiPlan.meditation_id,
    courseName: meditation.parent?.title || meditation.title,
    lessonName: meditation.title,
    cover: meditation.cover_url || 'https://picsum.photos/120/120?random=default',
    duration: Math.round(meditation.duration / 60), // 转换为分钟
    progress: apiPlan.completed ? 100 : 0,
    learnersCount: meditation.favorite_count || 0,
    isCompleted: apiPlan.completed
  }
}

// 计划数据
const planData = ref<Record<string, PlanItem[]>>({})
const loading = ref(false)

// 加载每日计划数据
const loadDailyPlans = async (date: string) => {
  try {
    loading.value = true
    const response = await PlanApi.getDailyPlans({ date })

    if (response.code === 200 && response.data) {
      // 清空现有数据
      planData.value = {}

      // 转换API数据到UI数据
      response.data.forEach((dailyPlan: DailyPlan) => {
        const uiPlans = dailyPlan.items.map(convertApiPlanToUiPlan)
        planData.value[dailyPlan.plan_date] = uiPlans
      })
    }
  } catch (error) {
    console.error('加载每日计划失败:', error)
    uni.showToast({
      title: '加载计划失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

const currentPlans = computed(() => {
  return planData.value[currentDate.value] || []
})

const completedCount = computed(() => {
  return currentPlans.value.filter((plan: PlanItem) => plan.isCompleted).length
})

const totalCount = computed(() => {
  return currentPlans.value.length
})

const progressPercent = computed(() => {
  if (totalCount.value === 0) return 0
  return Math.round((completedCount.value / totalCount.value) * 100)
})

// 监听当前日期变化，重新加载数据
watch(currentDate, (newDate) => {
  loadDailyPlans(newDate)
}, { immediate: false })

// 推荐课程数据
const recommendCourses = ref<MeditationContent[]>([])

// 加载推荐课程
const loadRecommendCourses = async () => {
  try {
    const response = await MeditationApi.getList({ pageNum: 1, pageSize: 4 })
    if (response.code === 200 && response.data) {
      recommendCourses.value = response.data.items
    }
  } catch (error) {
    console.error('加载推荐课程失败:', error)
    // 使用默认数据作为后备
    recommendCourses.value = [
      {
        id: 1,
        type: 'meditation',
        sub_type: 'course',
        parent_id: null,
        title: '深度放松',
        description: '释放一天的疲惫，让身心得到完全放松',
        cover_url: 'https://picsum.photos/280/160?random=r1',
        duration: 1080, // 18分钟 * 60秒
        tags_text: '放松,冥想',
        favorite_count: 156,
        created_at: new Date().toISOString(),
        updated_at: null
      },
      {
        id: 2,
        type: 'meditation',
        sub_type: 'course',
        parent_id: null,
        title: '情绪平衡',
        description: '学会管理情绪，保持内心的平静与和谐',
        cover_url: 'https://picsum.photos/280/160?random=r2',
        duration: 1320, // 22分钟 * 60秒
        tags_text: '情绪,平衡',
        favorite_count: 234,
        created_at: new Date().toISOString(),
        updated_at: null
      }
    ] as MeditationContent[]
  }
}

// 事件处理
const selectDate = (date: string) => {
  currentDate.value = date
}

const onPlanClick = (id: string) => {
  console.log('点击计划:', id)
  // 跳转到冥想详情页面
  uni.navigateTo({
    url: `/pages/meditation-detail/meditation-detail?id=${id}`
  })
}

const onCustomPlan = () => {
  console.log('定制计划')
  // 这里可以跳转到计划定制页面
}

const onCourseClick = (id: string) => {
  console.log('点击推荐课程:', id)
  // 跳转到冥想详情页面
  uni.navigateTo({
    url: `/pages/meditation-detail/meditation-detail?id=${id}`
  })
}

const onAddToPlan = async (id: string) => {
  console.log('加入计划:', id)

  try {
    const params: AddToPlanParams = {
      meditation_id: parseInt(id),
      plan_date: currentDate.value
    }

    const response = await PlanApi.addToPlan(params)

    if (response.code === 200) {
      uni.showToast({
        title: '已加入计划',
        icon: 'success'
      })

      // 重新加载当前日期的计划数据
      await loadDailyPlans(currentDate.value)
    }
  } catch (error) {
    console.error('加入计划失败:', error)
    uni.showToast({
      title: '加入计划失败',
      icon: 'none'
    })
  }
}

// 删除计划项
const onDeletePlan = async (planItemId: string) => {
  console.log('删除计划项:', planItemId)

  try {
    const response = await PlanApi.removeFromPlan(planItemId)

    if (response.code === 200) {
      uni.showToast({
        title: '已删除',
        icon: 'success'
      })

      // 重新加载当前日期的计划数据
      await loadDailyPlans(currentDate.value)
    }
  } catch (error) {
    console.error('删除计划失败:', error)
    uni.showToast({
      title: '删除失败',
      icon: 'none'
    })
  }
}

const onManagePlan = () => {
  console.log('进入计划管理页面')
  uni.navigateTo({
    url: '/pages/plan-management/plan-management'
  })
}

const onViewAllRecommend = () => {
  console.log('查看全部推荐课程')
  // 跳转到推荐课程列表页面
  uni.navigateTo({
    url: '/pages/recommend-list/recommend-list'
  })
}

// 页面生命周期
onMounted(() => {
  // 初始化加载数据
  loadDailyPlans(currentDate.value)
  loadRecommendCourses()
})

onShow(() => {
  const app = getApp()
  console.log(app.globalData.userInfo)

  // 每次显示页面时重新加载当前日期的计划数据
  loadDailyPlans(currentDate.value)
})
</script>

<style scoped>
.plan-page {
  padding-top: 176rpx;
  /* 状态栏 + 导航栏高度 */
  min-height: 100vh;
  background-color: #F7FAFC;
  display: flex;
  flex-direction: column;
}

.content {
  padding-top: 32rpx;
  flex: 1;
  padding-bottom: 150rpx;
  /* TabBar高度 */
  background-color: #F7FAFC;
}

.content-wrapper {
  padding: 0 32rpx;
}

/* 日期导航 */
.date-nav {
  display: flex;
  justify-content: space-between;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.date-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 8rpx;
  border-radius: 12rpx;
  transition: all 0.3s;
  position: relative;
}

.date-item.active {
  background: #7fb069;
}

.date-item.today:not(.active) {
  background: rgba(127, 176, 105, 0.1);
}

.date-day {
  font-size: 32rpx;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 8rpx;
}

.date-day.today-day {
  font-weight: 700;
  color: #7fb069;
}

.date-item.active .date-day {
  color: #ffffff;
  font-weight: 700;
}

.date-text {
  font-size: 22rpx;
  color: #718096;
}

.date-text.today-text {
  color: #7fb069;
  font-weight: 500;
}

.date-item.active .date-text {
  color: #ffffff;
}

/* 今天日期的小圆点标识 */
.date-item.today:not(.active)::after {
  content: '';
  position: absolute;
  bottom: 8rpx;
  width: 8rpx;
  height: 8rpx;
  background: #7fb069;
  border-radius: 50%;
}

/* 进度部分 */
.progress-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #2d3748;
}

.progress-text {
  font-size: 24rpx;
  color: #7fb069;
  font-weight: 500;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #e2e8f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #7fb069;
  border-radius: 4rpx;
  transition: width 0.3s;
}

/* 计划列表 */
.plan-list {
  margin-bottom: 32rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 24rpx;
}

/* 空状态 */
.empty-state {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 80rpx 32rpx;
  margin-bottom: 32rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.empty-title {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 16rpx;
}

.empty-desc {
  display: block;
  font-size: 26rpx;
  color: #718096;
  margin-bottom: 48rpx;
}

.custom-plan-button {
  width: 240rpx;
  height: 80rpx;
  background: #7fb069;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.button-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #ffffff;
}

/* 推荐课程 */
.recommend-section {
  margin-bottom: 32rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.view-all {
  font-size: 24rpx;
  color: #7fb069;
  font-weight: 500;
}

.manage-plan-button {
  font-size: 24rpx;
  color: #7fb069;
  font-weight: 500;
}

.recommend-scroll {
  white-space: nowrap;
  width: 100%;
}

/* 隐藏水平滚动条 */
.recommend-scroll::-webkit-scrollbar {
  display: none;
}

.recommend-scroll {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.recommend-container {
  display: flex;
  padding: 0 0 16rpx 0;
  width: max-content;
}
</style>