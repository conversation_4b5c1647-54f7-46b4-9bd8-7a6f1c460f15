<template>
  <view class="meditation-detail">
    <!-- 顶部导航 -->
    <view class="nav-bar">
      <view class="nav-back" @click="goBack">
        <text class="nav-back-icon">‹</text>
      </view>
      <text class="nav-title">冥想详情</text>
      <view class="nav-placeholder"></view>
    </view>

    <!-- 可滚动内容区域 -->
    <scroll-view class="scroll-content" scroll-y="true">
      <!-- 封面图片 -->
      <view class="cover-section">
        <image class="cover-image" :src="coverImage" mode="aspectFill" />
        <view class="cover-overlay">
          <text class="cover-title">{{ meditationData.title }}</text>
          <text class="cover-subtitle">{{ meditationData.duration }} · {{ meditationData.learners }}人正在学</text>
        </view>
      </view>

      <!-- 详情内容 -->
      <view class="content-section">
        <view class="info-card">
          <text class="info-title">课程介绍</text>
          <text class="info-description">{{ meditationData.description }}</text>
        </view>

        <view class="info-card">
          <text class="info-title">适合人群</text>
          <text class="info-description">{{ meditationData.suitableFor }}</text>
        </view>

        <view class="info-card">
          <text class="info-title">课程效果</text>
          <text class="info-description">{{ meditationData.effects }}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="bottom-actions">
      <button class="action-btn favorite-btn" @click="toggleFavorite">
        <text class="favorite-icon">{{ isFavorited ? '❤️' : '🤍' }}</text>
      </button>
      <button class="action-btn join-btn" @click="showJoinPlanModal">加入计划</button>
      <button class="action-btn start-btn" @click="startMeditation">立刻开始</button>
    </view>

    <!-- 加入计划弹窗 -->
    <view v-if="isJoinPlanModalVisible" class="modal-overlay" @click="hideJoinPlanModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">加入计划</text>
          <view class="modal-close" @click="hideJoinPlanModal">
            <text class="close-icon">×</text>
          </view>
        </view>
        
        <view class="modal-body">
          <view class="form-item">
            <text class="form-label">开始日期</text>
            <picker mode="date" :value="planForm.startDate" @change="onDateChange">
              <view class="picker-input">
                <text class="picker-text">{{ planForm.startDate || '请选择日期' }}</text>
                <text class="picker-arrow">›</text>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">安排时间</text>
            <picker mode="time" :value="planForm.time" @change="onTimeChange">
              <view class="picker-input">
                <text class="picker-text">{{ planForm.time || '请选择时间' }}</text>
                <text class="picker-arrow">›</text>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">类型</text>
            <view class="radio-group">
              <view 
                class="radio-item" 
                :class="{ active: planForm.type === 'repeat' }"
                @click="planForm.type = 'repeat'"
              >
                <view class="radio-circle">
                  <view v-if="planForm.type === 'repeat'" class="radio-dot"></view>
                </view>
                <text class="radio-text">循环</text>
              </view>
              <view 
                class="radio-item" 
                :class="{ active: planForm.type === 'once' }"
                @click="planForm.type = 'once'"
              >
                <view class="radio-circle">
                  <view v-if="planForm.type === 'once'" class="radio-dot"></view>
                </view>
                <text class="radio-text">一次</text>
              </view>
            </view>
          </view>
        </view>

        <view class="modal-footer">
          <button class="modal-btn cancel-btn" @click="hideJoinPlanModal">取消</button>
          <button class="modal-btn confirm-btn" @click="confirmJoinPlan">确认</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { MeditationApi, type MeditationContent } from '@/api/meditation'

// 冥想数据类型
interface MeditationDetail {
  id: string
  title: string
  type?: 'meditation' | 'sleep' | 'sound'
  index: number
  duration: string
  learners: number
  description: string
  suitableFor: string
  effects: string
}

// 冥想详情数据
const meditationData = ref<MeditationDetail>({
  id: '1',
  title: '正念呼吸冥想',
  type: 'meditation',
  index: 1,
  duration: '15分钟',
  learners: 1234,
  description: '通过专注于呼吸，帮助你放松身心，减轻压力和焦虑。这是一个适合初学者的基础冥想练习，能够有效提升专注力和内心平静。',
  suitableFor: '初学者、压力大的上班族、失眠人群、想要提升专注力的学生',
  effects: '减轻压力和焦虑、改善睡眠质量、提升专注力、增强情绪稳定性、培养内心平静'
})

// 加载状态
const loading = ref(false)

// 收藏状态
const isFavorited = ref(false)

// 加入计划弹窗状态
const isJoinPlanModalVisible = ref(false)

// 计划表单数据
const planForm = ref({
  startDate: '',
  time: '',
  type: 'repeat' // 'repeat' 或 'once'
})

// 计算封面图片路径
const coverImage = computed(() => {
  if (meditationData.value.type) {
    // 如果指定了类型，每个类型只有3张图片，需要循环使用
    const typeImageIndex = ((meditationData.value.index - 1) % 3) + 1
    const paddedIndex = String(typeImageIndex).padStart(2, '0')
    return `/static/images/${meditationData.value.type}-card-cover-${paddedIndex}.png`
  } else {
    // 如果没有指定类型，根据index循环显示所有图片
    const allImages = [
      'meditation-card-cover-01.png',
      'meditation-card-cover-02.png',
      'meditation-card-cover-03.png',
      'sleep-card-cover-01.png',
      'sleep-card-cover-02.png',
      'sleep-card-cover-03.png',
      'sound-card-cover-01.png',
      'sound-card-cover-02.png',
      'sound-card-cover-03.png'
    ]
    const imageIndex = (meditationData.value.index - 1) % allImages.length
    return `/static/images/${allImages[imageIndex]}`
  }
})

// 格式化时长显示
const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  return `${minutes}分钟`
}

// 从API数据转换为页面数据格式
const transformApiData = (apiData: MeditationContent): MeditationDetail => {
  return {
    id: apiData.id.toString(),
    title: apiData.title,
    type: apiData.type as 'meditation' | 'sleep' | 'sound',
    index: apiData.id, // 使用ID作为index
    duration: formatDuration(apiData.duration),
    learners: apiData.favorite_count || 0,
    description: apiData.description || '这是一个精心设计的冥想课程，帮助你在忙碌的生活中找到内心的平静与专注。',
    suitableFor: '所有想要改善生活质量的人群',
    effects: '减轻压力、提升幸福感、改善专注力、促进身心健康'
  }
}

onMounted(() => {
  // 根据路由参数获取具体的冥想详情
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options
  if (options && options.id) {
    loadMeditationDetail(options.id)
  }
})

// 根据id加载冥想详情
const loadMeditationDetail = async (id: string) => {
  try {
    loading.value = true
    const response = await MeditationApi.getDetail(parseInt(id))

    if (response.data) {
      meditationData.value = transformApiData(response.data)
      isFavorited.value = response.data.is_favorited || false
    }
  } catch (error) {
    console.error('加载冥想详情失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    })

    // 使用默认数据作为fallback
    meditationData.value = {
      id: id,
      title: '正念冥想课程',
      type: 'meditation',
      index: parseInt(id) || 1,
      duration: '15分钟',
      learners: 888,
      description: '这是一个精心设计的冥想课程，帮助你在忙碌的生活中找到内心的平静与专注。',
      suitableFor: '所有想要改善生活质量的人群',
      effects: '减轻压力、提升幸福感、改善专注力、促进身心健康'
    }
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 显示加入计划弹窗
const showJoinPlanModal = () => {
  isJoinPlanModalVisible.value = true
  // 设置默认日期为今天
  const today = new Date()
  const year = today.getFullYear()
  const month = String(today.getMonth() + 1).padStart(2, '0')
  const day = String(today.getDate()).padStart(2, '0')
  planForm.value.startDate = `${year}-${month}-${day}`
  
  // 设置默认时间为当前时间
  const hours = String(today.getHours()).padStart(2, '0')
  const minutes = String(today.getMinutes()).padStart(2, '0')
  planForm.value.time = `${hours}:${minutes}`
}

// 隐藏加入计划弹窗
const hideJoinPlanModal = () => {
  isJoinPlanModalVisible.value = false
}

// 日期选择变化
const onDateChange = (e: any) => {
  planForm.value.startDate = e.detail.value
}

// 时间选择变化
const onTimeChange = (e: any) => {
  planForm.value.time = e.detail.value
}

// 确认加入计划
const confirmJoinPlan = () => {
  if (!planForm.value.startDate || !planForm.value.time) {
    uni.showToast({
      title: '请完善计划信息',
      icon: 'none'
    })
    return
  }

  // 这里可以调用API保存计划
  console.log('计划信息:', planForm.value)
  
  uni.showToast({
    title: '已加入计划',
    icon: 'success'
  })
  
  hideJoinPlanModal()
}

// 开始冥想
const startMeditation = () => {
  uni.navigateTo({
    url: `/pages/meditation/meditation?id=${meditationData.value.id}`
  })
}

// 切换收藏状态
const toggleFavorite = async () => {
  try {
    const response = await MeditationApi.toggleFavorite(parseInt(meditationData.value.id))

    if (response.data) {
      isFavorited.value = response.data.is_favorited
      uni.showToast({
        title: isFavorited.value ? '已添加到收藏' : '已取消收藏',
        icon: 'success'
      })
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none'
    })
  }
}
</script>

<style scoped>
.meditation-detail {
  height: 100vh;
  background: #f7fafc;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 88rpx 32rpx 20rpx; /* 顶部增加状态栏高度 */
  background: #ffffff;
  flex-shrink: 0;
  z-index: 100;
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-back-icon {
  font-size: 48rpx;
  color: #2d3748;
  font-weight: bold;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
}

.nav-placeholder {
  width: 60rpx;
}

/* 滚动内容区域 */
.scroll-content {
  flex: 1;
  background: #f7fafc;
}

/* 封面区域 */
.cover-section {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 60rpx 32rpx 32rpx;
}

.cover-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 12rpx;
}

.cover-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 内容区域 */
.content-section {
  padding: 32rpx 32rpx 120rpx; /* 底部增加按钮高度的padding */
}

.info-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.info-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16rpx;
}

.info-description {
  font-size: 26rpx;
  color: #4a5568;
  line-height: 1.6;
}

/* 底部按钮 */
.bottom-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 24rpx 32rpx;
  background: #ffffff;
  border-top: 1rpx solid #e2e8f0;
  gap: 16rpx;
  flex-shrink: 0;
}

.action-btn {
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.favorite-btn {
  width: 88rpx;
  background: #fff5f5;
  color: #ff6b9d;
  border: 2rpx solid #ff6b9d;
}

.favorite-icon {
  font-size: 32rpx;
}

.join-btn {
  flex: 1;
  background: #e2e8f0;
  color: #4a5568;
}

.start-btn {
  flex: 1;
  background: #7fb069;
  color: #ffffff;
}

.action-btn:active {
  opacity: 0.8;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 32rpx;
}

.modal-content {
  background: #ffffff;
  border-radius: 24rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #e2e8f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24rpx;
  background: #f7fafc;
}

.close-icon {
  font-size: 36rpx;
  color: #718096;
  line-height: 1;
}

.modal-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 16rpx;
}

.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 20rpx;
  background: #f7fafc;
  border-radius: 12rpx;
  border: 1rpx solid #e2e8f0;
}

.picker-text {
  font-size: 26rpx;
  color: #2d3748;
}

.picker-arrow {
  font-size: 24rpx;
  color: #a0aec0;
  transform: rotate(90deg);
}

.radio-group {
  display: flex;
  gap: 32rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  background: #f7fafc;
  border: 1rpx solid #e2e8f0;
  flex: 1;
  justify-content: center;
  transition: all 0.2s;
}

.radio-item.active {
  background: #e6fffa;
  border-color: #7fb069;
}

.radio-circle {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  border: 2rpx solid #cbd5e0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.radio-item.active .radio-circle {
  border-color: #7fb069;
}

.radio-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #7fb069;
}

.radio-text {
  font-size: 26rpx;
  color: #2d3748;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx 32rpx;
  border-top: 1rpx solid #e2e8f0;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  background: #f7fafc;
  color: #4a5568;
  border: 1rpx solid #e2e8f0;
}

.confirm-btn {
  background: #7fb069;
  color: #ffffff;
}

.modal-btn:active {
  opacity: 0.8;
}
</style>