<template>
  <view class="meditation-detail">
    <!-- 顶部导航 -->
    <view class="nav-bar">
      <view class="nav-back" @click="goBack">
        <text class="nav-back-icon">‹</text>
      </view>
      <text class="nav-title">冥想详情</text>
      <view class="nav-placeholder"></view>
    </view>

    <!-- 可滚动内容区域 -->
    <scroll-view class="scroll-content" scroll-y="true">
      <!-- 封面图片 -->
      <view class="cover-section">
        <image class="cover-image" :src="coverImage" mode="aspectFill" />
        <view class="cover-overlay">
          <text class="cover-title">{{ apiData.title }}</text>
          <text class="cover-subtitle">{{ contentTypeText }} · {{ formatDuration(apiData.duration) }} · {{ apiData.favorite_count }}人收藏</text>
        </view>
      </view>

      <!-- 详情内容 -->
      <view class="content-section">
        <!-- 基本信息卡片 -->
        <view class="info-card">
          <text class="info-title">{{ isCourse ? '课程介绍' : '内容介绍' }}</text>
          <text class="info-description">{{ apiData.description || '暂无介绍' }}</text>
        </view>

        <!-- 标签显示 -->
        <view v-if="tagsArray.length > 0" class="info-card">
          <text class="info-title">标签</text>
          <view class="tags-container">
            <view v-for="tag in tagsArray" :key="tag" class="tag-item">
              <text class="tag-text">{{ tag }}</text>
            </view>
          </view>
        </view>

        <!-- 冥想课程列表 -->
        <view v-if="isCourse && apiData.children && apiData.children.length > 0" class="info-card">
          <text class="info-title">课程内容</text>
          <view class="course-list">
            <view
              v-for="(child, index) in apiData.children"
              :key="child.id"
              class="course-item"
              @click="goToCourseDetail(child.id)"
            >
              <image class="course-cover" :src="coverImage" mode="aspectFill" />
              <view class="course-info">
                <text class="course-title">{{ child.title }}</text>
                <text class="course-duration">{{ formatDuration(child.duration) }}</text>
              </view>
              <view class="course-arrow">
                <text class="arrow-icon">›</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="bottom-actions">
      <view class="action-btn favorite-btn" @click="toggleFavorite">
        <image
          class="favorite-icon"
          :src="isFavorited ? '/static/icons/like.png' : '/static/icons/unlike.png'"
          mode="aspectFit"
        />
      </view>
      <button class="action-btn join-btn" @click="showJoinPlanModal">加入计划</button>
      <button class="action-btn start-btn" @click="startMeditation">{{ isCourse ? '开始学习' : '立刻开始' }}</button>
    </view>

    <!-- 加入计划弹窗 -->
    <view v-if="isJoinPlanModalVisible" class="modal-overlay" @click="hideJoinPlanModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">加入计划</text>
          <view class="modal-close" @click="hideJoinPlanModal">
            <text class="close-icon">×</text>
          </view>
        </view>
        
        <view class="modal-body">
          <view class="form-item">
            <text class="form-label">开始日期</text>
            <picker mode="date" :value="planForm.startDate" @change="onDateChange">
              <view class="picker-input">
                <text class="picker-text">{{ planForm.startDate || '请选择日期' }}</text>
                <text class="picker-arrow">›</text>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">安排时间</text>
            <picker mode="time" :value="planForm.time" @change="onTimeChange">
              <view class="picker-input">
                <text class="picker-text">{{ planForm.time || '请选择时间' }}</text>
                <text class="picker-arrow">›</text>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">类型</text>
            <view class="radio-group">
              <view 
                class="radio-item" 
                :class="{ active: planForm.type === 'repeat' }"
                @click="planForm.type = 'repeat'"
              >
                <view class="radio-circle">
                  <view v-if="planForm.type === 'repeat'" class="radio-dot"></view>
                </view>
                <text class="radio-text">循环</text>
              </view>
              <view 
                class="radio-item" 
                :class="{ active: planForm.type === 'once' }"
                @click="planForm.type = 'once'"
              >
                <view class="radio-circle">
                  <view v-if="planForm.type === 'once'" class="radio-dot"></view>
                </view>
                <text class="radio-text">一次</text>
              </view>
            </view>
          </view>
        </view>

        <view class="modal-footer">
          <button class="modal-btn cancel-btn" @click="hideJoinPlanModal">取消</button>
          <button class="modal-btn confirm-btn" @click="confirmJoinPlan">确认</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { MeditationApi, type MeditationContent } from '@/api/meditation'

// 子课程接口
interface ChildCourse {
  id: string
  title: string
  duration: number
  cover_url?: string
}

// API数据结构（与后端响应保持一致）
interface ApiMeditationData {
  id: string
  type: 'meditation' | 'sleep' | 'sound'
  sub_type?: 'course' | 'single'
  parent_id?: string | null
  title: string
  description?: string
  cover_url?: string
  duration: number
  tags_text?: string
  favorite_count: number
  created_at: string
  updated_at?: string | null
  parent?: any
  children?: ChildCourse[]
  is_favorited: boolean
}

// API响应数据
const apiData = ref<ApiMeditationData>({
  id: '1',
  type: 'meditation',
  sub_type: 'course',
  parent_id: null,
  title: '正念冥想入门系列',
  description: '适合初学者的正念冥想课程，帮助你建立冥想习惯',
  cover_url: 'https://example.com/covers/mindfulness_course.jpg',
  duration: 0,
  tags_text: '正念,冥想入门,专注',
  favorite_count: 156,
  created_at: '2025-08-11T08:37:08.000Z',
  updated_at: null,
  parent: null,
  children: [],
  is_favorited: false
})

// 加载状态
const loading = ref(false)

// 收藏状态
const isFavorited = ref(false)

// 加入计划弹窗状态
const isJoinPlanModalVisible = ref(false)

// 计划表单数据
const planForm = ref({
  startDate: '',
  time: '',
  type: 'repeat' // 'repeat' 或 'once'
})

// 判断是否为课程类型
const isCourse = computed(() => {
  return apiData.value.type === 'meditation' && apiData.value.sub_type === 'course'
})

// 获取内容类型文本
const contentTypeText = computed(() => {
  const { type, sub_type } = apiData.value

  if (type === 'meditation') {
    return sub_type === 'course' ? '冥想课程' : '冥想'
  } else if (type === 'sleep') {
    return '助眠'
  } else if (type === 'sound') {
    return '声音'
  }
  return '冥想'
})

// 解析标签文本为数组
const tagsArray = computed(() => {
  if (!apiData.value.tags_text) return []
  return apiData.value.tags_text.split(',').filter(tag => tag.trim())
})

// 计算封面图片路径
const coverImage = computed(() => {
  // 优先使用API返回的封面图片
  // if (apiData.value.cover_url) {
  //   return apiData.value.cover_url
  // }

  // 如果没有封面图片，使用默认图片
  const type = apiData.value.type
  const id = parseInt(apiData.value.id) || 1
  const typeImageIndex = ((id - 1) % 3) + 1
  const paddedIndex = String(typeImageIndex).padStart(2, '0')
  return `/static/images/${type}-card-cover-${paddedIndex}.png`
})

// 格式化时长显示
const formatDuration = (seconds: number): string => {
  if (seconds === 0) return '课程合集'
  const minutes = Math.floor(seconds / 60)
  return `${minutes}分钟`
}

onMounted(() => {
  // 根据路由参数获取具体的冥想详情
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options
  if (options && options.id) {
    loadMeditationDetail(options.id)
  }
})

// 根据id加载冥想详情
const loadMeditationDetail = async (id: string) => {
  try {
    loading.value = true
    const response = await MeditationApi.getDetail(parseInt(id))

    if (response.data) {
      // 直接使用API返回的数据结构
      apiData.value = {
        id: response.data.id.toString(),
        type: response.data.type,
        sub_type: response.data.sub_type,
        parent_id: response.data.parent_id?.toString() || null,
        title: response.data.title,
        description: response.data.description,
        cover_url: response.data.cover_url,
        duration: response.data.duration,
        tags_text: response.data.tags_text,
        favorite_count: response.data.favorite_count,
        created_at: response.data.created_at,
        updated_at: response.data.updated_at,
        parent: response.data.parent,
        children: response.data.children || [],
        is_favorited: response.data.is_favorited || false
      }
      isFavorited.value = response.data.is_favorited || false
    }
  } catch (error) {
    console.error('加载冥想详情失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    })

    // 使用默认数据作为fallback
    apiData.value = {
      id: id,
      type: 'meditation',
      sub_type: 'single',
      parent_id: null,
      title: '正念冥想课程',
      description: '这是一个精心设计的冥想课程，帮助你在忙碌的生活中找到内心的平静与专注。',
      cover_url: '',
      duration: 900,
      tags_text: '正念,冥想,专注',
      favorite_count: 888,
      created_at: new Date().toISOString(),
      updated_at: null,
      parent: null,
      children: [],
      is_favorited: false
    }
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 显示加入计划弹窗
const showJoinPlanModal = () => {
  isJoinPlanModalVisible.value = true
  // 设置默认日期为今天
  const today = new Date()
  const year = today.getFullYear()
  const month = String(today.getMonth() + 1).padStart(2, '0')
  const day = String(today.getDate()).padStart(2, '0')
  planForm.value.startDate = `${year}-${month}-${day}`
  
  // 设置默认时间为当前时间
  const hours = String(today.getHours()).padStart(2, '0')
  const minutes = String(today.getMinutes()).padStart(2, '0')
  planForm.value.time = `${hours}:${minutes}`
}

// 隐藏加入计划弹窗
const hideJoinPlanModal = () => {
  isJoinPlanModalVisible.value = false
}

// 日期选择变化
const onDateChange = (e: any) => {
  planForm.value.startDate = e.detail.value
}

// 时间选择变化
const onTimeChange = (e: any) => {
  planForm.value.time = e.detail.value
}

// 确认加入计划
const confirmJoinPlan = () => {
  if (!planForm.value.startDate || !planForm.value.time) {
    uni.showToast({
      title: '请完善计划信息',
      icon: 'none'
    })
    return
  }

  // 这里可以调用API保存计划
  console.log('计划信息:', planForm.value)
  
  uni.showToast({
    title: '已加入计划',
    icon: 'success'
  })
  
  hideJoinPlanModal()
}

// 跳转到子课程详情
const goToCourseDetail = (courseId: string) => {
  uni.navigateTo({
    url: `/pages/meditation-detail/meditation-detail?id=${courseId}`
  })
}

// 开始冥想
const startMeditation = () => {
  if (isCourse.value && apiData.value.children && apiData.value.children.length > 0) {
    // 如果是课程，跳转到第一个子课程
    const firstCourse = apiData.value.children[0]
    uni.navigateTo({
      url: `/pages/meditation/meditation?id=${firstCourse.id}`
    })
  } else {
    // 如果是单个冥想，直接开始
    uni.navigateTo({
      url: `/pages/meditation/meditation?id=${apiData.value.id}`
    })
  }
}

// 切换收藏状态
const toggleFavorite = async () => {
  try {
    const response = await MeditationApi.toggleFavorite(parseInt(apiData.value.id))

    if (response.data) {
      isFavorited.value = response.data.is_favorited
      apiData.value.is_favorited = response.data.is_favorited
      uni.showToast({
        title: isFavorited.value ? '已添加到收藏' : '已取消收藏',
        icon: 'success'
      })
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none'
    })
  }
}
</script>

<style scoped>
.meditation-detail {
  height: 100vh;
  background: #f7fafc;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 88rpx 32rpx 20rpx; /* 顶部增加状态栏高度 */
  background: #ffffff;
  flex-shrink: 0;
  z-index: 100;
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-back-icon {
  font-size: 48rpx;
  color: #2d3748;
  font-weight: bold;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
}

.nav-placeholder {
  width: 60rpx;
}

/* 滚动内容区域 */
.scroll-content {
  flex: 1;
  background: #f7fafc;
}

/* 封面区域 */
.cover-section {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 60rpx 32rpx 32rpx;
}

.cover-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 12rpx;
}

.cover-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 内容区域 */
.content-section {
  padding: 32rpx 32rpx 120rpx; /* 底部增加按钮高度的padding */
}

.info-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.info-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16rpx;
}

.info-description {
  font-size: 26rpx;
  color: #4a5568;
  line-height: 1.6;
}

/* 底部按钮 */
.bottom-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 24rpx 32rpx;
  background: #ffffff;
  border-top: 1rpx solid #e2e8f0;
  gap: 16rpx;
  flex-shrink: 0;
}

.action-btn {
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.favorite-btn {
  width: 88rpx;
  background: #fefefe;
  /* color: #ff6b9d; */
  /* border: 2rpx solid #ff6b9d; */
}

.favorite-icon {
  width: 48rpx;
  height: 48rpx;
}

.join-btn {
  flex: 1;
  background: #e2e8f0;
  color: #4a5568;
}

.start-btn {
  flex: 1;
  background: #7fb069;
  color: #ffffff;
}

.action-btn:active {
  opacity: 0.8;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 32rpx;
}

.modal-content {
  background: #ffffff;
  border-radius: 24rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #e2e8f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24rpx;
  background: #f7fafc;
}

.close-icon {
  font-size: 36rpx;
  color: #718096;
  line-height: 1;
}

.modal-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 16rpx;
}

.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 20rpx;
  background: #f7fafc;
  border-radius: 12rpx;
  border: 1rpx solid #e2e8f0;
}

.picker-text {
  font-size: 26rpx;
  color: #2d3748;
}

.picker-arrow {
  font-size: 24rpx;
  color: #a0aec0;
  transform: rotate(90deg);
}

.radio-group {
  display: flex;
  gap: 32rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  background: #f7fafc;
  border: 1rpx solid #e2e8f0;
  flex: 1;
  justify-content: center;
  transition: all 0.2s;
}

.radio-item.active {
  background: #e6fffa;
  border-color: #7fb069;
}

.radio-circle {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  border: 2rpx solid #cbd5e0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.radio-item.active .radio-circle {
  border-color: #7fb069;
}

.radio-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #7fb069;
}

.radio-text {
  font-size: 26rpx;
  color: #2d3748;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx 32rpx;
  border-top: 1rpx solid #e2e8f0;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  background: #f7fafc;
  color: #4a5568;
  border: 1rpx solid #e2e8f0;
}

.confirm-btn {
  background: #7fb069;
  color: #ffffff;
}

.modal-btn:active {
  opacity: 0.8;
}

/* 标签样式 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 8rpx;
}

.tag-item {
  background: #e6fffa;
  border: 1rpx solid #7fb069;
  border-radius: 20rpx;
  padding: 6rpx 16rpx 8rpx 16rpx;
  line-height: 1;
}

.tag-text {
  height: 100%;
  font-size: 24rpx;
  color: #7fb069;
  font-weight: 500;
  line-height: 1;
}

/* 课程列表样式 */
.course-list {
  margin-top: 16rpx;
}

.course-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.course-item:last-child {
  border-bottom: none;
}

.course-item:active {
  background-color: #f8f9fa;
}

.course-cover {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.course-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.course-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #2d3748;
  line-height: 1.4;
}

.course-duration {
  font-size: 24rpx;
  color: #718096;
}

.course-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.arrow-icon {
  font-size: 28rpx;
  color: #a0aec0;
  font-weight: bold;
}
</style>